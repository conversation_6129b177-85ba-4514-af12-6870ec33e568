import { useState, useEffect } from 'react';

import TicketService from '../services/TicketService';
import { UserStore } from '../store/store';
import { Ticket } from '../types/ticket';

interface UseTicketsReturn {
  upcomingTickets: Ticket[];
  pastTickets: Ticket[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTickets = (): UseTicketsReturn => {
  const [upcomingTickets, setUpcomingTickets] = useState<Ticket[]>([]);
  const [pastTickets, setPastTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const user = UserStore((state: any) => state.user);

  const fetchTickets = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch tickets from API
      const ticketResponses = await TicketService.getTicketsByUser(user.id);

      // Categorize tickets into upcoming and past
      const { upcoming, past } = TicketService.categorizeTickets(ticketResponses);

      setUpcomingTickets(upcoming);
      setPastTickets(past);
    } catch (err: any) {
      console.error('Error fetching tickets:', err);
      setError(err.message || 'Failed to load tickets');
      setUpcomingTickets([]);
      setPastTickets([]);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await fetchTickets();
  };

  useEffect(() => {
    fetchTickets();
  }, [user?.id]);

  return {
    upcomingTickets,
    pastTickets,
    loading,
    error,
    refetch,
  };
};
