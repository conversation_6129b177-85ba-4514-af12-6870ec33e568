import axios from 'axios';

export interface PaymentStatus {
  reference: string;
  paynowreference: string;
  amount: string;
  status: 'Created' | 'Sent' | 'Paid' | 'Cancelled';
  pollurl: string;
  hash: string;
}

export interface PaymentResult {
  success: boolean;
  status: 'Paid' | 'Failed' | 'Timeout' | 'Cancelled';
  amount?: string;
  reference?: string;
}

export class PaymentPollingService {
  private static activePolls = new Map<
    string,
    {
      cancel: () => void;
      cancelled: { value: boolean };
    }
  >();

  private static parsePaymentResponse(responseText: string): PaymentStatus | null {
    try {
      // Parse the URL-encoded response
      const params = new URLSearchParams(responseText);

      return {
        reference: params.get('reference') || '',
        paynowreference: params.get('paynowreference') || '',
        amount: params.get('amount') || '',
        status: (params.get('status') as 'Created' | 'Sent' | 'Paid' | 'Cancelled') || 'Created',
        pollurl: decodeURIComponent(params.get('pollurl') || ''),
        hash: params.get('hash') || '',
      };
    } catch (error) {
      console.error('Error parsing payment response:', error);
      return null;
    }
  }

  private static async pollPaymentStatus(pollUrl: string): Promise<PaymentStatus | null> {
    try {
      const response = await axios.get(pollUrl, {
        timeout: 10000, // 10 second timeout
      });

      return this.parsePaymentResponse(response.data);
    } catch (error) {
      console.error('Error polling payment status:', error);
      return null;
    }
  }

  /**
   * Polls payment status until completion or timeout
   * @param pollUrl - The URL to poll for payment status
   * @param maxAttempts - Maximum number of polling attempts (default: 60)
   * @param intervalMs - Interval between polls in milliseconds (default: 3000)
   * @returns Promise<PaymentResult>
   */
  static async pollUntilComplete(
    pollUrl: string,
    maxAttempts: number = 60, // 3 minutes total
    intervalMs: number = 3000 // 3 seconds between polls
  ): Promise<PaymentResult> {
    let attempts = 0;
    let lastStatus: 'Created' | 'Sent' | 'Paid' | 'Cancelled' = 'Created';
    let amount = '';
    let reference = '';

    while (attempts < maxAttempts) {
      const status = await this.pollPaymentStatus(pollUrl);

      if (!status) {
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, intervalMs));
        continue;
      }

      amount = status.amount;
      reference = status.reference;

      console.log(`Payment polling attempt ${attempts + 1}: Status = ${status.status}`);

      // Track status progression
      if (status.status === 'Paid') {
        return {
          success: true,
          status: 'Paid',
          amount,
          reference,
        };
      }

      // Check for cancelled payment
      if (status.status === 'Cancelled') {
        return {
          success: false,
          status: 'Cancelled',
          amount,
          reference,
        };
      }

      // Check for failed transaction (Created -> Sent -> Created)
      if (lastStatus === 'Sent' && status.status === 'Created') {
        return {
          success: false,
          status: 'Failed',
          amount,
          reference,
        };
      }

      lastStatus = status.status;
      attempts++;

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }

    // Timeout reached
    return {
      success: false,
      status: 'Timeout',
      amount,
      reference,
    };
  }

  /**
   * Starts polling and returns a promise that resolves when payment is complete
   * Also provides a cancel function to stop polling
   */
  static startPolling(
    pollUrl: string,
    onStatusUpdate?: (status: PaymentStatus) => void
  ): {
    promise: Promise<PaymentResult>;
    cancel: () => void;
  } {
    const cancelled = { value: false };

    const cancel = () => {
      cancelled.value = true;
    };

    const promise = new Promise<PaymentResult>(async (resolve) => {
      let attempts = 0;
      const maxAttempts = 60; // 3 minutes
      const intervalMs = 3000; // 3 seconds
      let lastStatus: 'Created' | 'Sent' | 'Paid' | 'Cancelled' = 'Created';
      let amount = '';
      let reference = '';

      while (attempts < maxAttempts && !cancelled.value) {
        const status = await this.pollPaymentStatus(pollUrl);

        if (cancelled.value) break;

        if (!status) {
          attempts++;
          if (!cancelled.value) {
            await new Promise((resolve) => setTimeout(resolve, intervalMs));
          }
          continue;
        }

        amount = status.amount;
        reference = status.reference;

        // Call status update callback if provided
        if (onStatusUpdate && !cancelled.value) {
          onStatusUpdate(status);
        }

        console.log(`Payment polling attempt ${attempts + 1}: Status = ${status.status}`);

        // Check for successful payment
        if (status.status === 'Paid') {
          resolve({
            success: true,
            status: 'Paid',
            amount,
            reference,
          });
          return;
        }

        // Check for cancelled payment
        if (status.status === 'Cancelled') {
          resolve({
            success: false,
            status: 'Cancelled',
            amount,
            reference,
          });
          return;
        }

        // Check for failed transaction (Created -> Sent -> Created)
        if (lastStatus === 'Sent' && status.status === 'Created') {
          resolve({
            success: false,
            status: 'Failed',
            amount,
            reference,
          });
          return;
        }

        lastStatus = status.status;
        attempts++;

        // Wait before next poll
        if (!cancelled.value) {
          await new Promise((resolve) => setTimeout(resolve, intervalMs));
        }
      }

      // Timeout or cancelled
      if (cancelled.value) {
        console.log('Polling cancelled by user');
      }

      resolve({
        success: false,
        status: cancelled.value ? 'Failed' : 'Timeout',
        amount,
        reference,
      });
    });

    return { promise, cancel };
  }

  /**
   * Starts continuous polling and returns a promise that resolves when payment is complete
   * Also provides a cancel function to stop polling
   */
  static startBackgroundPolling(
    pollUrl: string,
    onStatusUpdate?: (status: PaymentStatus) => void
  ): {
    promise: Promise<PaymentResult>;
    cancel: () => void;
    pollId: string;
  } {
    const pollId = `poll_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const cancelled = { value: false };

    const cancel = () => {
      cancelled.value = true;
      // Remove from active polls
      this.activePolls.delete(pollId);
      console.log(`Polling cancelled for ${pollId}, active polls: ${this.activePolls.size}`);
    };

    // Add to active polls
    this.activePolls.set(pollId, { cancel, cancelled });

    const promise = new Promise<PaymentResult>(async (resolve) => {
      let attempts = 0;
      const maxAttempts = 60; // 3 minutes
      const intervalMs = 3000; // 3 seconds
      let lastStatus: 'Created' | 'Sent' | 'Paid' | 'Cancelled' = 'Created';
      let amount = '';
      let reference = '';

      console.log(
        `Starting continuous polling for ${pollId}, active polls: ${this.activePolls.size}`
      );

      while (attempts < maxAttempts && !cancelled.value) {
        try {
          const status = await this.pollPaymentStatus(pollUrl);

          if (cancelled.value) break;

          if (!status) {
            attempts++;
            if (!cancelled.value) {
              await new Promise((resolve) => setTimeout(resolve, intervalMs));
            }
            continue;
          }

          amount = status.amount;
          reference = status.reference;

          // Call status update callback if provided
          if (onStatusUpdate && !cancelled.value) {
            onStatusUpdate(status);
          }

          console.log(
            `Continuous polling attempt ${attempts + 1} for ${pollId}: Status = ${status.status}`
          );

          // Check for successful payment
          if (status.status === 'Paid') {
            console.log(`Payment successful for ${pollId}`);
            resolve({
              success: true,
              status: 'Paid',
              amount,
              reference,
            });
            cancel(); // Clean up
            return;
          }

          // Check for cancelled payment
          if (status.status === 'Cancelled') {
            console.log(`Payment cancelled for ${pollId}`);
            resolve({
              success: false,
              status: 'Cancelled',
              amount,
              reference,
            });
            cancel(); // Clean up
            return;
          }

          // Check for failed transaction (Created -> Sent -> Created)
          if (lastStatus === 'Sent' && status.status === 'Created') {
            console.log(`Payment failed for ${pollId}`);
            resolve({
              success: false,
              status: 'Failed',
              amount,
              reference,
            });
            cancel(); // Clean up
            return;
          }

          lastStatus = status.status;
          attempts++;

          // Wait before next poll
          if (!cancelled.value) {
            await new Promise((resolve) => setTimeout(resolve, intervalMs));
          }
        } catch (error) {
          console.error(`Polling error for ${pollId}:`, error);
          if (!cancelled.value) {
            attempts++;
            await new Promise((resolve) => setTimeout(resolve, intervalMs));
          }
        }
      }

      // Timeout or cancelled
      console.log(`Polling ended for ${pollId}: ${cancelled.value ? 'Cancelled' : 'Timeout'}`);
      resolve({
        success: false,
        status: cancelled.value ? 'Failed' : 'Timeout',
        amount,
        reference,
      });
      cancel(); // Clean up
    });

    return { promise, cancel, pollId };
  }

  /**
   * Cancel all active polling operations
   */
  static cancelAllPolling(): void {
    const pollCount = this.activePolls.size;
    console.log(`Cancelling ${pollCount} active polls`);

    this.activePolls.forEach(({ cancel }, pollId) => {
      console.log(`Cancelling polling operation: ${pollId}`);
      try {
        cancel();
      } catch (error) {
        console.error(`Error cancelling polling operation ${pollId}:`, error);
      }
    });

    this.activePolls.clear();
    console.log(`All polling operations cancelled. Active count: ${this.activePolls.size}`);
  }

  /**
   * Cancel a specific polling operation by ID
   */
  static cancelPolling(pollId: string): boolean {
    const poll = this.activePolls.get(pollId);
    if (poll) {
      console.log(`Cancelling specific polling operation: ${pollId}`);
      try {
        poll.cancel();
        this.activePolls.delete(pollId);
        return true;
      } catch (error) {
        console.error(`Error cancelling polling operation ${pollId}:`, error);
        return false;
      }
    }
    console.log(`Polling operation ${pollId} not found in active polls`);
    return false;
  }

  /**
   * Get the number of active polling operations
   */
  static getActivePollingCount(): number {
    return this.activePolls.size;
  }
}
