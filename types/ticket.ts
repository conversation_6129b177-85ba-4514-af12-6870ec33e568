export interface TicketImage {
  id: string;
  secureUrl: string;
  url: string;
  width: string;
  height: string;
}

export interface EventCategory {
  id: string;
  categoryName: string;
}

export interface EventUser {
  id: string;
  fullName: string;
  email: string;
  profilePicture: TicketImage[];
}

export interface LocationData {
  coordinates: number[];
  name: string;
}

export interface TicketLevel {
  quantity: number;
  price: number;
  type: string;
}

export interface TicketSetup {
  hasLevels: boolean;
  levels: TicketLevel[];
}

export interface Event {
  id: string;
  title: string;
  description: string;
  location: string;
  locationData: LocationData;
  startDateTime: string;
  endDateTime: string;
  coverImages: TicketImage[];
  eventCategory: EventCategory;
  user: EventUser;
  ticketSetup: TicketSetup;
  currency: string;
  paid: boolean;
}

export interface Transaction {
  id: string;
  transactionType: string;
  transactionStatus: string;
  amount: number;
  referenceNumber: string;
}

export interface TicketResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  event: Event;
  user: EventUser;
  transaction: Transaction | null;
  currency: string | null;
  ticketType: string;
  price: number;
  paid: boolean;
  transactionReferenceNumber: string;
  ticketNumber: string;
  ticketStatus: 'PAID' | 'FAILED' | 'PENDING';
}

export interface TicketApiResponse {
  success: boolean;
  message: string;
  body: TicketResponse[];
}

// Transformed ticket for UI display
export interface Ticket {
  id: string;
  eventName: string;
  date: string;
  time: string;
  endTime?: string;
  location: string;
  ticketType: string;
  seat: string;
  price: string;
  qrCode: string;
  eventImage: string;
  orderNumber: string;
  attendeeName: string;
  organizer: string;
  venue: string;
  eventSummary: string;
  ticketStatus: 'PAID' | 'FAILED' | 'PENDING';
  rawEvent: Event;
}
