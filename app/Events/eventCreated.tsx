import React from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useColorScheme } from '~/lib/useColorScheme';
import LocationPreview from '~/components/Map/LocationPreview';
import { useEventStore } from '~/store/store';

export default function EventCreatedScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const eventData = useEventStore((state) => state.eventData);

  const formatDate = (dateInput: string | Date) => {
    const date = new Date(dateInput);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateInput: string | Date) => {
    const date = new Date(dateInput);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
  };

  const handleViewEvent = () => {
    // Navigate to event details page
    router.push(`/(drawer)/(tabs)`);
  };

  const handleInviteGuests = () => {
    // Open share dialog or navigate to invite screen
    // This would be implemented based on your app's sharing functionality
  };

  const handleShareEvent = () => {
    // Open native share dialog
    // This would be implemented based on your app's sharing functionality
  };

  const handlePromoteEvent = () => {
    // Navigate to promotion screen
    router.push({
      pathname: '/Events/promoteEvent',
      params: { eventId: eventData?.id || '' },
    });
  };

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 15,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Close button */}
        <View className="absolute z-10 left-6 top-4">
          <TouchableOpacity
            onPress={() => router.push('/(drawer)/(tabs)')}
            className={`rounded-full p-2 ${isDark ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <MaterialIcons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Success content */}
        <View className="items-center px-6 pt-12">
          <View
            className={`mb-6 items-center justify-center rounded-full p-4 ${isDark ? 'bg-gray-800' : 'bg-gray-100'}`}
            style={{ width: 80, height: 80 }}>
            <MaterialIcons name="check" size={40} color={isDark ? '#8b5cf6' : '#7c3aed'} />
          </View>

          <Text
            className="mb-2 text-3xl font-bold text-center"
            style={{ color: colors.foreground }}>
            Your Event is Created 🎉
          </Text>

          <Text className="mb-8 text-base text-center" style={{ color: colors.grey }}>
            Promote, spread the word, and start welcoming guests on board!
          </Text>

          {/* Event details card */}
          <View
            className={`w-full overflow-hidden rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'}`}
            style={{
              shadowColor: colors.foreground,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3,
            }}>
            <View className="p-4">
              <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
                {eventData.title}
              </Text>

              <Text className="mt-1" style={{ color: colors.grey }}>
                {formatDate(eventData.startDateTime)} at {formatTime(eventData.endDateTime)}
              </Text>

              <Text className="mt-1" style={{ color: colors.grey }}>
                {eventData.location}
              </Text>
            </View>

            {/* Map preview */}
            <View className="h-[150px] w-full">
              <LocationPreview location={eventData.locationData} />
            </View>
          </View>

          {/* Action button - View Event */}

          {/* Action buttons row */}
          {/* <View className="flex-row justify-between w-full mt-4">
            <TouchableOpacity
              className={`flex-1 rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} mr-2 p-4`}
              style={{
                shadowColor: colors.foreground,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 2,
              }}
              onPress={handleInviteGuests}>
              <Text
                className="text-base font-medium text-center"
                style={{ color: colors.foreground }}>
                Invite Guests
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`flex-1 rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} ml-2 p-4`}
              style={{
                shadowColor: colors.foreground,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 2,
              }}
              onPress={handleShareEvent}>
              <Text
                className="text-base font-medium text-center"
                style={{ color: colors.foreground }}>
                Share Event
              </Text>
            </TouchableOpacity>
          </View> */}

          {/* Promote Event Button */}
          <TouchableOpacity
            className={`mt-8 w-full rounded-xl bg-violet-600 p-4`}
            onPress={handlePromoteEvent}>
            <Text className="text-lg font-medium text-center text-white">Promote Event</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`mt-8 w-full rounded-xl ${isDark ? 'bg-gray-800' : 'bg-white'} p-4`}
            style={{
              shadowColor: colors.foreground,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 2,
            }}
            onPress={handleViewEvent}>
            <Text className="text-lg font-medium text-center" style={{ color: colors.foreground }}>
              Continue Without Promoting
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
