import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { useNavigation, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState, useRef, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import ViewShot, { captureRef } from 'react-native-view-shot';
import { Toast } from 'toastify-react-native';

import { RenderBackdrop } from '~/components/RenderBackdrop';
import { Button } from '~/components/ui/button';
import { useTickets } from '~/hooks/useTickets';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ticket } from '~/types/ticket';

export default function TicketsScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [bottomSheetIndex, setBottomSheetIndex] = useState(-1);

  // Fetch tickets using the custom hook
  const { upcomingTickets, pastTickets, loading, error, refetch } = useTickets();

  // Bottom sheet reference and snap points
  const ticketBottomSheetRef = useRef<BottomSheet>(null);
  const viewShotRef = useRef<ViewShot>(null);
  const snapPoints = useMemo(() => ['90%'], []);

  const handleViewTicket = useCallback((ticket: Ticket) => {
    setSelectedTicket(ticket);
    // Set the index to 0 to show the sheet
    setBottomSheetIndex(0);
  }, []);

  // Change back to hidden when closed
  const handleSheetChanges = useCallback((index: number) => {
    setBottomSheetIndex(index);
  }, []);

  // Download ticket as image
  const downloadTicketAsImage = useCallback(async () => {
    try {
      // Request permissions for saving to photos
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Please allow photo library access to save the ticket.');
        return;
      }

      // Capture the view as image
      const uri = await captureRef(viewShotRef, {
        format: 'png',
        quality: 0.8,
      });

      // Save to photo library
      const asset = await MediaLibrary.createAssetAsync(uri);
      await MediaLibrary.createAlbumAsync('Tickets', asset, false);

      Toast.success('Ticket saved to photos!', 'top');
    } catch (error) {
      console.error('Error saving ticket:', error);
      Toast.error('Failed to save ticket. Please try again.', 'top');
    }
  }, []);

  // Check if event has ended
  const isEventEnded = useCallback((ticket: Ticket) => {
    const eventEndTime = new Date(ticket.rawEvent.endDateTime);
    const now = new Date();
    return eventEndTime < now;
  }, []);

  // Open location in maps
  const openInGoogleMaps = useCallback((ticket: Ticket) => {
    if (!ticket.rawEvent?.locationData?.coordinates) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Location coordinates not available',
        position: 'bottom',
      });
      return;
    }

    // Coordinates are stored as [longitude, latitude]
    const [longitude, latitude] = ticket.rawEvent.locationData.coordinates;
    const address = ticket.rawEvent.location || '';

    let url: string;

    if (Platform.OS === 'ios') {
      // For iOS, use Apple Maps or Google Maps app
      url = `maps://app?daddr=${latitude},${longitude}`;
      // Alternative Google Maps URL for iOS: `comgooglemaps://?daddr=${latitude},${longitude}`
    } else {
      // For Android, use Google Maps
      url = `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(address)})`;
    }

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // Fallback to web Google Maps
          const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => {
        console.error('Error opening maps:', err);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Could not open maps application',
          position: 'bottom',
        });
      });
  }, []);

  // Navigate to event details
  const viewEventListing = useCallback((ticket: Ticket) => {
    router.push({
      pathname: '/Events/viewEvent',
      params: { eventId: ticket.rawEvent.id },
    });
  }, []);

  const renderTicketBottomSheet = () => {
    if (!selectedTicket) return null;

    return (
      <BottomSheet
        ref={ticketBottomSheetRef}
        index={bottomSheetIndex}
        snapPoints={snapPoints}
        enablePanDownToClose
        onChange={handleSheetChanges}
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetScrollView style={{ flex: 1 }}>
          <View className="flex-row items-center justify-between p-4">
            <TouchableOpacity onPress={() => setBottomSheetIndex(-1)}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
            <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
              My Tickets
            </Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView className="flex-1">
            <ViewShot ref={viewShotRef} options={{ format: 'png', quality: 0.8 }}>
              <View className="items-center justify-center p-8 bg-indigo-500/10">
                <Image
                  source={{ uri: selectedTicket.eventImage }}
                  className="object-cover w-full h-24 opacity-30"
                  style={{ position: 'absolute', top: 0, left: 0, right: 0 }}
                />
                <View className="items-center justify-center">
                  <Text
                    className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                    {selectedTicket.eventName}
                  </Text>
                </View>
              </View>

              <View className="items-center w-full p-4">
                <View
                  className="items-center w-full p-4 mb-4 rounded-lg"
                  style={{ backgroundColor: colors.grey5 }}>
                  <Image
                    source={{ uri: selectedTicket.qrCode }}
                    className="mb-6 h-52 w-52"
                    style={{ backgroundColor: 'white' }}
                  />

                  <View className="w-full pt-4 border-t border-gray-300 border-dashed dark:border-gray-700">
                    <Text className="mb-2 text-gray-500 dark:text-gray-400">Name</Text>
                    <Text
                      className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                      {selectedTicket.attendeeName}
                    </Text>

                    <Text className="mb-2 text-gray-500 dark:text-gray-400">Event</Text>
                    <Text
                      className={`font-bold text-xl ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                      {selectedTicket.eventName}
                    </Text>

                    <Text className="mb-2 text-gray-500 dark:text-gray-400">Price</Text>
                    <Text
                      className={`font-bold text-lg ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                      {selectedTicket.price}
                    </Text>

                    <Text className="mb-2 text-gray-500 dark:text-gray-400">Ticket/seat</Text>
                    <Text
                      className={`font-bold text-lg ${isDark ? 'text-white' : 'text-indigo-900'} mb-6`}>
                      {selectedTicket.seat}
                    </Text>

                    <View className="flex-row mb-6">
                      <View className="flex-1">
                        <Text className="mb-2 text-gray-500 dark:text-gray-400">Date</Text>
                        <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                          {selectedTicket.date}
                        </Text>
                        <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                          {selectedTicket.time}{' '}
                          {selectedTicket.endTime ? `— ${selectedTicket.endTime}` : ''}
                        </Text>
                      </View>

                      <View className="flex-1">
                        <Text className="mb-2 text-gray-500 dark:text-gray-400">Venue</Text>
                        <Text className={`text-base ${isDark ? 'text-white' : 'text-indigo-900'}`}>
                          {selectedTicket.venue}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>

                <View className="w-full mb-5">
                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Order number</Text>
                  <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    #{selectedTicket.orderNumber}
                  </Text>
                </View>

                <View className="w-full mb-5">
                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Event summary</Text>
                  <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    {selectedTicket.eventSummary}
                  </Text>
                </View>

                <View className="w-full mb-5">
                  <Text className="mb-2 text-gray-500 dark:text-gray-400">Organiser</Text>
                  <View className="flex-row items-center justify-between">
                    <Text className={`text-base ${isDark ? 'text-white' : 'text-black'}`}>
                      {selectedTicket.organizer}
                    </Text>
                  </View>
                </View>
              </View>
            </ViewShot>

            {/* Interactive buttons outside of ViewShot */}
            <View className="p-4">
              {!isEventEnded(selectedTicket) && (
                <View className="mb-4">
                  <TouchableOpacity
                    className="p-3 mb-2 bg-blue-500 rounded-lg"
                    onPress={() => openInGoogleMaps(selectedTicket)}>
                    <Text className="font-medium text-center text-white">View Map</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    className="p-3 bg-green-500 rounded-lg"
                    onPress={() => viewEventListing(selectedTicket)}>
                    <Text className="font-medium text-center text-white">View Event Listing</Text>
                  </TouchableOpacity>
                </View>
              )}

              <Button
                size="lg"
                variant="solid"
                className={`mt-4 h-14 rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={downloadTicketAsImage}>
                <Text className="font-bold text-white">Save ticket as image</Text>
              </Button>
            </View>
          </ScrollView>
        </BottomSheetScrollView>
      </BottomSheet>
    );
  };

  const renderTickets = () => {
    const currentTickets = activeTab === 'upcoming' ? upcomingTickets : pastTickets;

    if (loading) {
      return (
        <View className="items-center justify-center mt-24">
          <View className="items-center justify-center w-24 h-24 mb-5 bg-gray-100 rounded-full dark:bg-gray-800">
            <MaterialCommunityIcons
              name="ticket-confirmation-outline"
              size={40}
              color={isDark ? '#aaa' : '#ccc'}
            />
          </View>
          <Text
            className={`mb-5 text-center font-medium text-xl ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Loading tickets...
          </Text>
        </View>
      );
    }

    if (error) {
      return (
        <View className="items-center justify-center mt-24">
          <View className="items-center justify-center w-24 h-24 mb-5 bg-red-100 rounded-full dark:bg-red-800">
            <MaterialCommunityIcons
              name="alert-circle-outline"
              size={40}
              color={isDark ? '#f87171' : '#dc2626'}
            />
          </View>
          <Text
            className={`mb-5 text-center font-medium text-xl ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Error loading tickets
          </Text>
          <Text
            className={`mb-7 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {error}
          </Text>
          <TouchableOpacity className="px-10 py-3 rounded-lg bg-violet-600" onPress={refetch}>
            <Text className="text-base font-medium text-white">Try again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (currentTickets.length === 0) {
      return (
        <View className="items-center justify-center mt-24">
          <View className="items-center justify-center w-24 h-24 mb-5 bg-gray-100 rounded-full dark:bg-gray-800">
            <MaterialCommunityIcons
              name="ticket-confirmation-outline"
              size={40}
              color={isDark ? '#aaa' : '#ccc'}
            />
          </View>
          <Text
            className={`mb-5 text-center font-medium text-xl ${isDark ? 'text-white' : 'text-gray-800'}`}>
            No tickets yet
          </Text>
          <Text
            className={`mb-7 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {activeTab === 'upcoming'
              ? 'You have no upcoming events. Purchase tickets to see them here.'
              : "You have no past events. Events you've attended will appear here."}
          </Text>
          <TouchableOpacity className="px-10 py-3 bg-gray-200 rounded-lg dark:bg-gray-700">
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-gray-800'}`}>
              Browse events
            </Text>
          </TouchableOpacity>

          <TouchableOpacity className="mt-10">
            <Text className="text-center text-blue-500">Visit our help centre</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return currentTickets.map((ticket, index) => (
      <TouchableOpacity
        key={ticket.id}
        className="flex-row items-center pb-4 mt-5 border-b border-gray-200 dark:border-gray-700"
        onPress={() => handleViewTicket(ticket)}>
        <View className="relative w-16 h-16 mr-4 overflow-hidden rounded-md">
          <View className="absolute top-0 left-0 z-10 flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full dark:bg-gray-700">
            <Text className={`font-medium text-sm ${isDark ? 'text-white' : 'text-gray-800'}`}>
              {index + 1}
            </Text>
          </View>
          <Image source={{ uri: ticket.eventImage }} className="w-full h-full" />
        </View>

        <View className="flex-1">
          <Text className={`mb-1 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            {ticket.eventName}
          </Text>
          <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {ticket.date} • {ticket.time}
          </Text>
        </View>

        <View>
          <Text className="text-blue-500">View ticket</Text>
        </View>
      </TouchableOpacity>
    ));
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View className="flex-1" style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        {/* Header with Back Button */}
        <View className="flex-row items-center justify-between px-4 pt-12 pb-2">
          <TouchableOpacity onPress={() => navigation.goBack()} className="p-1">
            <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Tickets
          </Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Tabs */}
        <View className="flex-row border-b border-gray-200 dark:border-gray-800">
          <TouchableOpacity
            className={`flex-1 items-center justify-center py-4 ${activeTab === 'upcoming' ? 'border-b-2 border-blue-500' : ''}`}
            onPress={() => setActiveTab('upcoming')}>
            <Text
              className={`font-medium ${
                activeTab === 'upcoming'
                  ? 'text-blue-500'
                  : isDark
                    ? 'text-gray-400'
                    : 'text-gray-500'
              }`}>
              Upcoming
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 items-center justify-center py-4 ${activeTab === 'past' ? 'border-b-2 border-blue-500' : ''}`}
            onPress={() => setActiveTab('past')}>
            <Text
              className={`font-medium ${
                activeTab === 'past' ? 'text-blue-500' : isDark ? 'text-gray-400' : 'text-gray-500'
              }`}>
              Past tickets
            </Text>
          </TouchableOpacity>
        </View>

        {/* Ticket List */}
        <ScrollView
          className="flex-1 px-4"
          refreshControl={<RefreshControl refreshing={loading} onRefresh={refetch} />}>
          {renderTickets()}
        </ScrollView>

        {/* Ticket Detail Bottom Sheet */}
        {renderTicketBottomSheet()}
      </View>
    </GestureHandlerRootView>
  );
}
